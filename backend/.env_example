BASE_URL=https://llmui.pride.improwised.dev
API_KEY=<your_api_key>

MOCK_QUESTION_COUNT=10
FINAL_QUESTION_COUNT=20
TOTAL_QUESTIONS_COUNT=30

# Individual difficulty level question counts
EASY_QUESTIONS_COUNT=10
INTERMEDIATE_QUESTIONS_COUNT=10
ADVANCED_QUESTIONS_COUNT=10
MODEL_ID='qwen/qwen3-30b-a3b'
PYTHONUNBUFFERED=1

# PostgreSQL database credentials
PG_USER=herbit
PG_PASSWORD=herbit
PG_DATABASE=herbit
PG_HOST=localhost
PG_PORT=5432

## Auth Client Configuration
AUTH_CLIENT_ID=example-app
AUTH_CLIENT_SECRET=ZXhhbXBsZS1hcHAtc2VjcmV0

# Dex
AUTH_ISSUER=http://127.0.0.1:5556
AUTH_TOKEN_URL=http://127.0.0.1:5556/token
AUTH_AUTHORIZE_URL=http://127.0.0.1:5556/auth
AUTH_LOGOUT_URL=http://127.0.0.1:5556/logout
AUTH_REDIRECT_URI=http://localhost:5173/callback
AUTH_JWKS_URI=http://127.0.0.1:5556/keys

# Application URLs
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173

# Worker and Dapr Service Endpoints
WORKER_ENDPOINT=http://localhost:8001
DAPR_ENDPOINT=http://localhost:3500

# Legacy variables (kept for compatibility)
WORKER_PORT=8001
WORKER_HOST=localhost
DAPR_HTTP_PORT=3500

# Worker Configuration
QUESTION_GENERATION_TIMEOUT=600  # Timeout in seconds for question generation (default: 10 minutes)

# Logging Configuration
LOG_LEVEL=INFO
DEBUG=false
DEBUG_LOG=false

services:
  db:
    image: postgres:13
    container_name: herbit-db
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: ${PG_DATABASE}
      POSTGRES_USER: ${PG_USER}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - herbit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PG_USER} -d ${PG_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:7-alpine
    container_name: herbit-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    networks:
      - herbit-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: herbit-backend
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      dex:
        condition: service_started
    env_file:
      - ./backend/.env
    environment:
      POSTGRES_DB: ${PG_DATABASE}
      POSTGRES_USER: ${PG_USER}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
      MIGRATIONS_DIR: "backend/database/migrations"
      PG_HOST: db
      DOCKER_ENV: "true"
      WORKER_ENDPOINT: http://worker:8001
      DAPR_ENDPOINT: http://localhost:3500
      # Override Auth URLs for Docker environment
      AUTH_ISSUER: http://dex:5556
      AUTH_TOKEN_URL: http://dex:5556/token
      AUTH_AUTHORIZE_URL: http://dex:5556/auth
      AUTH_LOGOUT_URL: http://dex:5556/logout
      AUTH_JWKS_URI: http://dex:5556/keys
    ports:
      - "8000:8000"
      - "3500:3500" # Expose Dapr HTTP port
    networks:
      - herbit-network
    volumes:
      - ./dapr/components:/app/dapr/components

  backend-dapr:
    image: "daprio/daprd:1.15.5"
    container_name: herbit-backend-dapr
    command:
      [
        "./daprd",
        "-app-id",
        "herbit-api",
        "-app-port",
        "8000",
        "-dapr-http-port",
        "3500",
        "-dapr-grpc-port",
        "50001",
        "-dapr-listen-addresses",
        "0.0.0.0",
        "-resources-path",
        "/components",
        "-config",
        "/config/config.yaml",
        "-log-level",
        "info",
      ]
    depends_on:
      - backend
      - redis
    network_mode: "service:backend"
    volumes:
      - "./dapr/components/:/components"
      - "./dapr/config.yaml:/config/config.yaml"

  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
    container_name: herbit-unified-worker
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - ./backend/.env
    environment:
      PG_HOST: db
      WORKER_PORT: 8001
    ports:
      - "8001:8001"
      - "3501:3501"  # Expose Dapr HTTP port for worker
    networks:
      - herbit-network
    volumes:
      - ./dapr/components:/app/dapr/components
      - ./config:/app/config
      
  worker-dapr:
    image: "daprio/daprd:1.15.5"
    container_name: herbit-worker-dapr
    command: [
      "./daprd",
      "-app-id", "herbit-worker",
      "-app-port", "8001",
      "-dapr-http-port", "3501",
      "-dapr-grpc-port", "50002",
      "-dapr-listen-addresses", "0.0.0.0",
      "-resources-path", "/components",
      "-config", "/config/config.yaml",
      "-log-level", "info"
    ]
    depends_on:
      - worker
      - redis
    network_mode: "service:worker"
    volumes:
      - "./dapr/components/:/components"
      - "./dapr/config.yaml:/config/config.yaml"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: herbit-frontend
    restart: unless-stopped
    depends_on:
      - backend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://backend:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - herbit-network

  adminer:
    image: adminer:latest
    container_name: herbit-adminer
    restart: unless-stopped
    depends_on:
      - db
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha
    networks:
      - herbit-network

  ldap:
    image: osixia/openldap:latest
    container_name: herbit-ldap
    command: ["--copy-service"]
    environment:
      LDAP_ORGANISATION: "Example Inc."
      LDAP_DOMAIN: "example.org"
      LDAP_ADMIN_PASSWORD: admin
      LDAP_TLS_VERIFY_CLIENT: try
    volumes:
      - ./auth/dex/config-ldap.ldif:/container/service/slapd/assets/config/bootstrap/ldif/custom/config-ldap.ldif
    ports:
      - "1389:389"
      - "1636:636"
    networks:
      - herbit-network

  dex:
    image: ghcr.io/dexidp/dex:v2.37.0
    container_name: herbit-dex
    command: ["dex", "serve", "/etc/dex/config.yaml"]
    depends_on:
      - ldap
    ports:
      - "5556:5556"
    volumes:
      - ./auth/dex/config-ldap.yaml:/etc/dex/config.yaml
    networks:
      - herbit-network

networks:
  herbit-network:
    driver: bridge

volumes:
  db-data:
    name: herbit-db-data
  dex-data:
    name: herbit-dex-data
